System Updates for AMIS Five
Based on the codebase context, here are the refined system update requirements:

Activity Management Updates
New Activity Type: Add "Output" as a fourth activity type alongside existing types (training, inputs, infrastructure)
Output Activity Structure: Include fields for item description, quantity, unit, date, and remarks
Activity Linking Workflow: After creating an activity, automatically redirect to the linking page
"Others" Linking Option: Add an "Others" category for linking activities that don't fit into NASP, MTDP, or Corporate plans (e.g., recurrent activities)
Activity Assignment Restriction: Implement validation to prevent assigning unlinked activities to users (must be linked to a plan or "Others")
Admin Functionality
Expanded Activity View: Allow admins to view all activities from both supervisors and officers
PDF Export: Add PDF generation functionality to all activity pages with proper document layout
Reporting Enhancements
MTDP Report Filters: Add filtering by SPA, DIP, Specific Area, and date range
NASP Report Filters: Similar filtering capabilities as MTDP reports
Dynamic Visualization: Update charts and graphs to respond to applied filters
PDF Export: Add PDF export functionality to all report pages
Additional Report Types:
HR Reports (gender distribution, join date statistics)
Government Structure Reports
SME Reports with map integration (using existing GPS coordinates)
Commodity price trend reports for commodity boards
Corporate Plan Structure
Update the Corporate Plan structure to align with new activity types and linking requirements
These updates will enhance the system's reporting capabilities, improve activity management workflow, and provide better visualization of agricultural activities across Papua New Guinea.