{"system_overview": {"name": "AMIS Five - Agricultural Management Information System", "version": "5.0", "description": "Comprehensive agricultural management system for Papua New Guinea's Department of Agriculture and Livestock", "framework": "CodeIgniter 4", "architecture": "MVC (Model-View-Controller)", "deployment_environment": "XAMPP (Apache, MySQL, PHP)", "primary_purpose": "Manage workplans, activities, proposals, reports, and agricultural data across government structures"}, "technical_stack": {"backend": {"framework": "CodeIgniter 4", "language": "PHP 8+", "database": "MySQL", "server": "Apache (XAMPP)"}, "frontend": {"css_framework": "Bootstrap 5.3.0", "javascript_libraries": ["jQuery 3.6.0", "DataTables 1.13.6", "Select2 4.1.0", "<PERSON><PERSON><PERSON> (notifications)", "Font Awesome 6.4.0"], "fonts": "Google Fonts - Inter (300,400,500,600,700)", "icons": "Font Awesome 6.4.0"}, "additional_libraries": ["OpenStreetMap (for mapping)", "Chart.js (for reports)", "Bootstrap modals and components"]}, "design_system": {"color_palette": {"primary_green": "#6ba84f", "light_green": "#8bc34a", "dark_green": "#558b2f", "navy_blue": "#1a237e", "light_navy": "#283593", "dark_navy": "#0d47a1", "success_color": "#43a047", "warning_color": "#fdd835", "danger_color": "#e53935", "light_bg": "#f5f7fa"}, "layout": {"sidebar_width": "250px", "sidebar_collapsed_width": "70px", "header_height": "60px", "transition_speed": "0.3s", "card_shadow": "0 4px 6px rgba(0, 0, 0, 0.1)"}, "typography": {"font_family": "Inter, sans-serif", "base_color": "#2c3e50"}, "ui_patterns": {"status_badges": "Dark text on light backgrounds with borders", "buttons": "Bootstrap 5 button classes with Font Awesome icons", "tables": "Bootstrap table-bordered table-hover with counter numbers (#)", "cards": "Bootstrap cards with shadows for content containers", "forms": "Standard CodeIgniter form helpers with Bootstrap styling", "notifications": "Toastr for success/error messages"}}, "user_roles_and_permissions": {"roles": [{"name": "admin", "description": "Full system access, can manage all modules and users", "permissions": ["create", "read", "update", "delete", "manage_users", "system_settings"]}, {"name": "supervisor", "description": "Can supervise workplans and approve proposals", "permissions": ["create", "read", "update", "supervise", "approve_proposals"]}, {"name": "user", "description": "Standard user with limited access to their own data", "permissions": ["create", "read", "update_own"]}, {"name": "guest", "description": "Read-only access to limited data", "permissions": ["read_limited"]}, {"name": "commodity", "description": "Specialized role for commodity board management", "permissions": ["commodity_management", "read", "update"]}], "special_flags": {"is_evaluator": "M&E (Monitoring & Evaluation) access for rating activities", "report_to_id": "Hierarchical reporting structure"}}, "core_modules": {"workplans": {"description": "Central planning module for agricultural activities", "features": ["CRUD operations", "Activity management", "Supervisor assignment", "Status tracking"], "activity_types": ["training", "infrastructure", "inputs"], "relationships": ["NASP links", "MTDP links", "Corporate Plan links"]}, "activities": {"description": "Detailed activity management with GPS tracking", "types": {"training": "Training activities with trainers and trainees", "infrastructure": "Infrastructure development projects", "inputs": "Agricultural input distribution"}, "features": ["GPS coordinates", "File uploads", "Implementation tracking", "Proposal generation"]}, "proposals": {"description": "Activity proposal and approval workflow", "workflow": ["pending", "submitted", "approved", "rated", "rejected"], "features": ["Supervisor approval", "M&E rating", "Email notifications"]}, "documents": {"description": "File and folder management system", "features": ["Folder hierarchy", "File uploads", "Access control", "File type tracking"]}, "meetings": {"description": "Meeting scheduling and management", "features": ["CRUD operations", "Participant tracking", "Branch-based filtering"]}, "agreements": {"description": "Agreement and contract management", "features": ["CRUD operations", "File attachments", "Status tracking"]}, "smes": {"description": "Small and Medium Enterprise management", "features": ["SME registration", "Staff management", "Logo uploads", "Location tracking"]}, "commodities": {"description": "Commodity production and board management", "features": ["Production tracking", "Dashboard interface", "Role-based access"]}}, "planning_frameworks": {"nasp": {"name": "National Agriculture Strategic Plan", "hierarchy": ["Plans", "APAs (Agriculture Priority Areas)", "DIPs (Deliberate Intervention Programs)", "Specific Areas", "Outputs", "Indicators"], "purpose": "National-level agricultural planning framework"}, "mtdp": {"name": "Medium Term Development Plan", "hierarchy": ["Plan", "SPA", "DIP", "Specific Area", "Investments", "KRA", "Strategies", "Indicators"], "purpose": "Medium-term development planning framework"}, "corporate_plan": {"name": "Corporate Plan", "hierarchy": ["Plan", "Overarching Objectives", "Objectives", "KRAs", "Strategy"], "purpose": "Organizational strategic planning"}}, "government_structure": {"hierarchy": ["Province", "District", "LLG (Local Level Government)", "Ward"], "features": ["Hierarchical management", "CSV import functionality", "Code and name fields"], "purpose": "Papua New Guinea government administrative structure"}, "reporting_system": {"types": ["Workplan Reports", "Activity Reports", "NASP Reports", "MTDP Reports", "Commodity Reports", "Activity Maps (OpenStreetMap integration)"], "features": ["Charts and graphs", "Data visualization", "Export capabilities", "GPS mapping"]}, "file_organization": {"controllers": {"location": "app/Controllers/", "admin_controllers": "app/Controllers/Admin/", "naming_convention": "PascalCase with 'Controller' suffix", "patterns": ["RESTful methods", "Separate GET/POST methods", "Standard CRUD operations"]}, "models": {"location": "app/Models/", "naming_convention": "PascalCase with 'Model' suffix", "patterns": ["CodeIgniter 4 Model class", "Validation rules", "Relationship methods"]}, "views": {"location": "app/Views/", "organization": "Folder per module", "naming_convention": "module_prefix + action (e.g., workplan_index.php, activities_create.php)", "template": "app/Views/templates/system_template.php"}, "routes": {"location": "app/Config/Routes.php", "patterns": ["RESTful routing", "Resource controllers", "Admin route groups"]}}, "naming_conventions": {"view_files": {"pattern": "{folder_name}_{action}.php", "examples": ["workplan_index.php", "workplan_create.php", "activities_show.php", "proposals_edit.php"]}, "database_tables": {"pattern": "snake_case", "examples": ["workplan_activities", "gov_structure", "mtdp_strategies"]}, "css_classes": {"status_badges": "status-{status_name}", "custom_styles": "kebab-case"}}, "common_ui_components": {"tables": {"structure": "Bootstrap table-bordered table-hover inside cards", "headers": "Counter column (#) instead of ID display", "actions": "View, Edit, Delete buttons with icons"}, "forms": {"framework": "CodeIgniter form helpers with Bootstrap styling", "validation": "Frontend validation preferred, simple server-side rules", "submission": "Standard form submission (no AJAX unless specified)"}, "status_indicators": {"style": "Dark text on light backgrounds with borders", "colors": "Semantic colors for different statuses"}, "navigation": {"sidebar": "Collapsible sidebar with icons and text", "breadcrumbs": "Removed from most views, back buttons preferred", "user_menu": "Dropdown with profile and logout options"}, "notifications": {"library": "Toastr", "types": ["success", "error", "warning", "info"]}, "modals": {"framework": "Bootstrap 5 modals", "usage": "Complex confirmations and searchable dropdowns"}}, "development_patterns": {"authentication": {"system": "Session-based authentication", "password_policy": "Minimum 4 characters (simple validation)", "features": ["Password reset via email", "Temporary passwords", "Role-based access"]}, "email_system": {"method": "CodeIgniter built-in email with SMTP", "configuration": "SMTP for dakoiims.com (port 465)", "notifications": ["User account updates", "Workplan supervisor notifications", "Proposal status changes", "Activity submissions and ratings"]}, "file_uploads": {"location": "public/uploads/ folder", "database_storage": "File paths stored with 'public/' prefix", "types": ["Images", "Documents", "Signing sheets", "User photos"]}, "validation": {"approach": "Simple validation functions preferred", "frontend": "JavaScript validation for dates and basic fields", "backend": "CodeIgniter validation rules (minimal complexity)"}, "database_operations": {"approach": "Standard CodeIgniter 4 CRUD operations", "relationships": "Model methods for related data", "soft_deletes": "deleted_at timestamps used"}}, "implemented_features": {"user_management": {"status": "Complete", "features": ["CRUD operations", "Role assignment", "Photo uploads", "Status tracking", "Email notifications"]}, "workplan_system": {"status": "Complete", "features": ["Workplan CRUD", "Activity management", "Plan linking (NASP/MTDP/Corporate)", "Supervisor assignment", "Plan link indicators in activities list"]}, "activity_management": {"status": "Complete", "features": ["Three activity types", "GPS tracking", "Implementation forms", "File uploads", "Proposal generation"]}, "proposal_workflow": {"status": "Complete", "features": ["Approval workflow", "M&E rating", "Email notifications", "Status tracking"]}, "document_management": {"status": "Complete", "features": ["Folder hierarchy", "File uploads", "Access control", "File metadata"]}, "reporting_system": {"status": "Complete", "features": ["Multiple report types", "Charts and visualization", "Activity mapping", "Data export"]}, "government_structure": {"status": "Complete", "features": ["Hierarchical management", "CSV import", "CRUD operations"]}, "planning_frameworks": {"status": "Complete", "features": ["NASP management", "MTDP management", "Corporate Plan management", "Hierarchical structures"]}}, "current_system_state": {"stability": "Production-ready", "deployment": "XAMPP environment", "database": "MySQL with comprehensive schema", "user_interface": "Responsive Bootstrap 5 design", "functionality": "Full CRUD operations across all modules", "integration": "Email notifications, file management, GPS mapping", "security": "Role-based access control, session management", "maintenance": "Active development with regular updates"}, "development_guidelines": {"database_changes": "Do not modify existing database structure", "new_features": "Use existing models, follow established patterns", "ui_consistency": "Maintain Bootstrap 5 design system and naming conventions", "form_handling": "Standard CodeIgniter form submission (avoid AJAX)", "file_paths": "Always include 'public/' prefix for uploaded files", "code_style": "Simple, straightforward CodeIgniter 4 patterns", "testing": "XAMPP environment with base URL testing"}}