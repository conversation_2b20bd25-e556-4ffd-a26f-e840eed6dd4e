AMIS Five System Updates: Features & Functions
Features List
Output Activity Type
New activity type alongside training, inputs, and infrastructure
Structured data fields for outputs (description, quantity, unit, date, remarks)
Enhanced Activity Linking
Automatic redirection to linking page after activity creation
"Others" category for activities outside formal planning frameworks
Visual indicators for linked/unlinked activities
Activity Assignment Controls
Validation system for activity assignments
Plan-linking requirement enforcement
Admin Activity Dashboard
Consolidated view of supervisor and officer activities
Enhanced filtering capabilities
PDF Document Generation
PDF export for all activity pages
Optimized document layouts for printing
Advanced Reporting System
Granular filtering for MTDP and NASP reports
Dynamic data visualization based on filters
Specialized report types (HR, Government Structure, SME, Commodity)
Map integration for SME locations
Corporate Plan Structure Updates
Revised structure supporting new activity types
Improved linking between corporate plans and activities
Functions List
createOutputActivity()
Handle creation of output-type activities
Validate output-specific fields
redirectToLinkingPage($activityId)
Automatic redirection after activity creation
Pre-populate linking form with activity data
linkActivityToOthers($activityId, $description)
Link activities to "Others" category
Store justification for non-standard linking
validateActivityAssignment($activityId)
Check if activity is linked before assignment
Return validation status and messages
getAdminActivityView()
Retrieve consolidated activity data
Include supervisor and officer activities
generateActivityPDF($activityId)
Create formatted PDF of activity details
Include relevant linked plans and status
applyReportFilters($reportType, $filters)
Process filter parameters for reports
Return filtered dataset
generateDynamicCharts($dataset, $chartType)
Create responsive visualizations
Update based on applied filters
exportReportToPDF($reportId, $filters)
Generate PDF version of filtered reports
Format for printing and sharing
displaySMEMap($filters)
Show SMEs on interactive map
Apply district and other filters
updateCorporatePlanStructure()
Modify corporate plan data structure
Ensure compatibility with new activity types